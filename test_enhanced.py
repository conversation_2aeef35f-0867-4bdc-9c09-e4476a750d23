"""
测试增强版OCR功能
"""
import re

# 验证码配置
CAPTCHA_CONFIG = {
    'length': 4,
    'chars': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
    'case_sensitive': False,
}

def validate_and_filter_result(text, config=CAPTCHA_CONFIG):
    """验证和过滤识别结果"""
    if not text:
        return None
    
    # 移除空格和特殊字符
    cleaned = re.sub(r'[^a-zA-Z0-9]', '', text)
    
    # 大小写处理
    if not config['case_sensitive']:
        cleaned = cleaned.lower()
    
    # 只保留允许的字符
    allowed_chars = config['chars'].lower() if not config['case_sensitive'] else config['chars']
    filtered = ''.join(c for c in cleaned if c in allowed_chars)
    
    # 长度检查
    if len(filtered) == config['length']:
        return filtered
    elif len(filtered) > config['length']:
        return filtered[:config['length']]
    else:
        return filtered if filtered else None

def calculate_confidence_score(results, config=CAPTCHA_CONFIG):
    """计算识别结果的置信度分数"""
    # 过滤和标准化结果
    filtered_results = []
    for method, text in results:
        filtered_text = validate_and_filter_result(text, config)
        if filtered_text:
            filtered_results.append((method, filtered_text))
    
    if not filtered_results:
        return []
    
    # 统计每个结果的出现次数
    result_counts = {}
    for method, text in filtered_results:
        if text in result_counts:
            result_counts[text]['count'] += 1
            result_counts[text]['methods'].append(method)
        else:
            result_counts[text] = {'count': 1, 'methods': [method]}
    
    # 计算置信度分数
    scored_results = []
    total_attempts = len(filtered_results)
    
    for text, data in result_counts.items():
        # 基础分数：出现频率
        frequency_score = data['count'] / total_attempts
        
        # 长度匹配奖励
        length_score = 1.0 if len(text) == config['length'] else 0.5
        
        # 字符有效性奖励
        allowed_chars = config['chars'].lower() if not config['case_sensitive'] else config['chars']
        valid_chars = sum(1 for c in text if c in allowed_chars)
        char_score = valid_chars / len(text) if text else 0
        
        # 综合分数
        final_score = frequency_score * 0.6 + length_score * 0.3 + char_score * 0.1
        
        scored_results.append({
            'text': text,
            'count': data['count'],
            'methods': data['methods'],
            'frequency_score': frequency_score,
            'length_score': length_score,
            'char_score': char_score,
            'final_score': final_score
        })
    
    # 按最终分数排序
    scored_results.sort(key=lambda x: x['final_score'], reverse=True)
    return scored_results

def test_with_sample_data():
    """使用示例数据测试"""
    # 模拟识别结果（类似之前的实际结果）
    sample_results = [
        ('original', 'bgvb'),
        ('enlarged_2x', 'bgvb'),
        ('adaptive_threshold', 'bgv8'),
        ('enhanced_contrast', 'bgvb'),
        ('sharpened', 'bgvb'),
        ('method6', 'BGVB'),  # 大写版本
        ('method7', 'bgv b'),  # 带空格
        ('method8', 'bgv'),    # 长度不足
        ('method9', 'bgvb1'),  # 长度超出
    ]
    
    print("🔍 验证码智能识别测试")
    print("=" * 50)
    print("原始识别结果:")
    for method, result in sample_results:
        print(f"  {method}: '{result}'")
    
    # 计算置信度分数
    scored_results = calculate_confidence_score(sample_results)
    
    print("\n智能分析结果:")
    print("=" * 50)
    print(f"{'排名':<4} {'结果':<8} {'出现次数':<8} {'频率':<8} {'长度匹配':<10} {'最终分数':<10}")
    print("-" * 50)
    
    for i, result in enumerate(scored_results, 1):
        print(f"{i:<4} {result['text']:<8} {result['count']:<8} "
              f"{result['frequency_score']:.2f}{'':4} {result['length_score']:.2f}{'':6} "
              f"{result['final_score']:.3f}")
    
    if scored_results:
        best_result = scored_results[0]
        print(f"\n🎯 最佳识别结果: '{best_result['text']}'")
        print(f"   置信度分数: {best_result['final_score']:.3f}")
        print(f"   识别方法: {', '.join(best_result['methods'])}")
        
        if best_result['final_score'] >= 0.8:
            print("   ✅ 高置信度结果")
        elif best_result['final_score'] >= 0.6:
            print("   ⚠️  中等置信度结果")
        else:
            print("   ❌ 低置信度结果")

if __name__ == "__main__":
    test_with_sample_data()
