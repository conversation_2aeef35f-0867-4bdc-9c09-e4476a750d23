"""
安装OCR相关依赖的脚本
"""
import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ 成功安装 {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ 安装 {package} 失败")
        return False

def main():
    print("开始安装OCR相关依赖...")
    
    # 需要安装的包
    packages = [
        "opencv-python",
        "ddddocr",
        "easyocr", 
        "pytesseract",
        "Pillow",
        "numpy"
    ]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("\n所有依赖安装成功！可以运行 enhanced_ocr.py 了")
    else:
        print("\n部分依赖安装失败，请手动安装失败的包")
    
    # Tesseract 安装提示
    print("\n注意: 如果要使用 Tesseract，还需要:")
    print("1. 下载安装 Tesseract-OCR: https://github.com/UB-Mannheim/tesseract/wiki")
    print("2. 将 Tesseract 添加到系统 PATH 环境变量")

if __name__ == "__main__":
    main()
