"""
验证码识别配置文件
可以根据不同的验证码类型调整参数
"""

# 默认配置 - 4位数字+字母验证码
DEFAULT_CONFIG = {
    'length': 4,  # 验证码长度
    'chars': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',  # 允许的字符
    'case_sensitive': False,  # 是否区分大小写
}

# 纯数字验证码配置
NUMERIC_CONFIG = {
    'length': 4,
    'chars': '0123456789',
    'case_sensitive': False,
}

# 纯字母验证码配置
ALPHA_CONFIG = {
    'length': 4,
    'chars': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
    'case_sensitive': False,
}

# 6位数字+字母验证码配置
LONG_ALPHANUMERIC_CONFIG = {
    'length': 6,
    'chars': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
    'case_sensitive': False,
}

# 区分大小写的配置
CASE_SENSITIVE_CONFIG = {
    'length': 4,
    'chars': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
    'case_sensitive': True,
}

def get_config(config_name='default'):
    """
    获取指定的配置
    
    Args:
        config_name: 配置名称 ('default', 'numeric', 'alpha', 'long', 'case_sensitive')
    
    Returns:
        配置字典
    """
    configs = {
        'default': DEFAULT_CONFIG,
        'numeric': NUMERIC_CONFIG,
        'alpha': ALPHA_CONFIG,
        'long': LONG_ALPHANUMERIC_CONFIG,
        'case_sensitive': CASE_SENSITIVE_CONFIG,
    }
    
    return configs.get(config_name, DEFAULT_CONFIG)

def create_custom_config(length=4, include_numbers=True, include_lowercase=True, 
                        include_uppercase=True, case_sensitive=False):
    """
    创建自定义配置
    
    Args:
        length: 验证码长度
        include_numbers: 是否包含数字
        include_lowercase: 是否包含小写字母
        include_uppercase: 是否包含大写字母
        case_sensitive: 是否区分大小写
    
    Returns:
        自定义配置字典
    """
    chars = ''
    
    if include_numbers:
        chars += '0123456789'
    
    if include_lowercase:
        chars += 'abcdefghijklmnopqrstuvwxyz'
    
    if include_uppercase:
        chars += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    
    return {
        'length': length,
        'chars': chars,
        'case_sensitive': case_sensitive,
    }

def print_config_info(config):
    """打印配置信息"""
    print("验证码配置信息:")
    print(f"  长度: {config['length']} 位")
    print(f"  允许字符: {config['chars']}")
    print(f"  区分大小写: {'是' if config['case_sensitive'] else '否'}")
    
    # 分析字符类型
    has_numbers = any(c.isdigit() for c in config['chars'])
    has_lowercase = any(c.islower() for c in config['chars'])
    has_uppercase = any(c.isupper() for c in config['chars'])
    
    char_types = []
    if has_numbers:
        char_types.append("数字")
    if has_lowercase:
        char_types.append("小写字母")
    if has_uppercase:
        char_types.append("大写字母")
    
    print(f"  字符类型: {' + '.join(char_types)}")

if __name__ == "__main__":
    print("验证码配置示例:")
    print("=" * 40)
    
    # 显示所有预设配置
    config_names = ['default', 'numeric', 'alpha', 'long', 'case_sensitive']
    
    for name in config_names:
        print(f"\n{name.upper()} 配置:")
        config = get_config(name)
        print_config_info(config)
    
    # 自定义配置示例
    print(f"\n自定义配置示例:")
    custom = create_custom_config(length=5, include_numbers=True, 
                                 include_lowercase=True, include_uppercase=False)
    print_config_info(custom)
