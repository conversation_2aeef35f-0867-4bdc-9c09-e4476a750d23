"""
简化的验证码识别脚本，直接识别c.png
"""
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import ddddocr
import os

def preprocess_image(image_path):
    """图像预处理"""
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"无法读取图像: {image_path}")
    
    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 多种预处理方法
    processed_images = []
    
    # 1. 原始灰度图
    processed_images.append(("original", gray))
    
    # 2. 高斯模糊 + 锐化
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(blurred, -1, kernel)
    processed_images.append(("sharpened", sharpened))
    
    # 3. 自适应阈值
    adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    processed_images.append(("adaptive", adaptive))
    
    # 4. OTSU阈值
    _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    processed_images.append(("otsu", otsu))
    
    # 5. 对比度增强
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    processed_images.append(("enhanced", enhanced))
    
    return processed_images

def recognize_with_ddddocr(image_path):
    """使用ddddocr识别"""
    try:
        ocr = ddddocr.DdddOcr()
    except:
        print("ddddocr 初始化失败")
        return []
    
    results = []
    
    # 预处理图像
    processed_images = preprocess_image(image_path)
    
    for name, img in processed_images:
        # 尝试不同的放大倍数
        for scale in [1.0, 2.0, 3.0]:
            try:
                if scale != 1.0:
                    height, width = img.shape[:2]
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    resized = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
                else:
                    resized = img
                
                # 转换为字节
                _, img_bytes = cv2.imencode('.png', resized)
                
                # 识别
                result = ocr.classification(img_bytes.tobytes())
                
                if result and result.strip():
                    method_name = f"{name}_scale{scale}"
                    results.append((method_name, result.strip()))
                    print(f"{method_name}: {result.strip()}")
                    
            except Exception as e:
                print(f"{name}_scale{scale} 失败: {e}")
    
    return results

def main():
    image_path = "c.png"
    
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    print(f"开始识别图像: {image_path}")
    print("="*50)
    
    # 使用ddddocr识别
    results = recognize_with_ddddocr(image_path)
    
    if results:
        print("\n" + "="*50)
        print("识别结果统计:")
        print("="*50)
        
        # 统计结果
        result_counts = {}
        for method, text in results:
            if text in result_counts:
                result_counts[text] += 1
            else:
                result_counts[text] = 1
        
        # 按出现次数排序
        sorted_results = sorted(result_counts.items(), key=lambda x: x[1], reverse=True)
        
        print("按置信度排序的结果:")
        for text, count in sorted_results:
            print(f"  '{text}': 出现 {count} 次")
        
        if sorted_results:
            best_result = sorted_results[0][0]
            print(f"\n最可能的结果: '{best_result}'")
        
        print(f"\n总尝试次数: {len(results)}")
    else:
        print("未能识别出任何结果")

if __name__ == "__main__":
    main()
