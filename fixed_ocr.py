"""
修复PIL兼容性问题的OCR脚本
"""
import cv2
import numpy as np
from PIL import Image
import ddddocr
import os

# 修复PIL兼容性问题
if not hasattr(Image, 'ANTIALIAS'):
    Image.ANTIALIAS = Image.LANCZOS

def enhance_image_for_ocr(image_path):
    """图像增强处理"""
    # 使用OpenCV读取和处理
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"无法读取图像: {image_path}")
    
    results = []
    
    # 1. 原始图像
    with open(image_path, 'rb') as f:
        original_bytes = f.read()
    results.append(("original", original_bytes))
    
    # 2. 转换为灰度并放大
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 放大2倍
    height, width = gray.shape
    enlarged = cv2.resize(gray, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    
    # 保存临时文件并读取字节
    temp_path = "temp_enlarged.png"
    cv2.imwrite(temp_path, enlarged)
    with open(temp_path, 'rb') as f:
        enlarged_bytes = f.read()
    results.append(("enlarged_2x", enlarged_bytes))
    os.remove(temp_path)
    
    # 3. 自适应阈值处理
    adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    
    # 放大并保存
    adaptive_enlarged = cv2.resize(adaptive, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    temp_path = "temp_adaptive.png"
    cv2.imwrite(temp_path, adaptive_enlarged)
    with open(temp_path, 'rb') as f:
        adaptive_bytes = f.read()
    results.append(("adaptive_threshold", adaptive_bytes))
    os.remove(temp_path)
    
    # 4. 对比度增强
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    enhanced_enlarged = cv2.resize(enhanced, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    
    temp_path = "temp_enhanced.png"
    cv2.imwrite(temp_path, enhanced_enlarged)
    with open(temp_path, 'rb') as f:
        enhanced_bytes = f.read()
    results.append(("enhanced_contrast", enhanced_bytes))
    os.remove(temp_path)
    
    # 5. 锐化处理
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(gray, -1, kernel)
    sharpened_enlarged = cv2.resize(sharpened, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    
    temp_path = "temp_sharpened.png"
    cv2.imwrite(temp_path, sharpened_enlarged)
    with open(temp_path, 'rb') as f:
        sharpened_bytes = f.read()
    results.append(("sharpened", sharpened_bytes))
    os.remove(temp_path)
    
    return results

def recognize_captcha(image_path):
    """识别验证码"""
    print(f"开始识别图像: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"文件不存在: {image_path}")
        return
    
    try:
        # 初始化OCR
        ocr = ddddocr.DdddOcr()
        print("ddddocr 初始化成功")
        
        # 获取不同处理方式的图像
        processed_images = enhance_image_for_ocr(image_path)
        
        results = []
        
        for method_name, img_bytes in processed_images:
            try:
                result = ocr.classification(img_bytes)
                if result and result.strip():
                    results.append((method_name, result.strip()))
                    print(f"{method_name}: '{result.strip()}'")
                else:
                    print(f"{method_name}: 无结果")
            except Exception as e:
                print(f"{method_name} 识别失败: {e}")
        
        # 统计结果
        if results:
            print("\n" + "="*50)
            print("识别结果统计:")
            print("="*50)
            
            result_counts = {}
            for method, text in results:
                if text in result_counts:
                    result_counts[text] += 1
                else:
                    result_counts[text] = 1
            
            # 按出现次数排序
            sorted_results = sorted(result_counts.items(), key=lambda x: x[1], reverse=True)
            
            for text, count in sorted_results:
                print(f"'{text}': 出现 {count} 次")
            
            if sorted_results:
                best_result = sorted_results[0][0]
                print(f"\n最可能的结果: '{best_result}'")
                return best_result
        else:
            print("所有方法都未能识别出结果")
            return None
            
    except Exception as e:
        print(f"识别过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    # 修复PIL兼容性
    if not hasattr(Image, 'ANTIALIAS'):
        Image.ANTIALIAS = Image.LANCZOS
    
    image_path = "c.png"
    result = recognize_captcha(image_path)
    
    if result:
        print(f"\n最终识别结果: {result}")
    else:
        print("\n识别失败")

if __name__ == "__main__":
    main()