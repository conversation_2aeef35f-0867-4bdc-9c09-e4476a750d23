"""
修复PIL兼容性问题的OCR脚本
支持数字+字母验证码识别，可配置验证码长度
"""
import cv2
import numpy as np
from PIL import Image
import ddddocr
import os
import re

# 修复PIL兼容性问题
if not hasattr(Image, 'ANTIALIAS'):
    Image.ANTIALIAS = Image.LANCZOS

# 验证码配置
CAPTCHA_CONFIG = {
    'length': 4,  # 验证码长度，可调整
    'chars': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',  # 允许的字符
    'case_sensitive': False,  # 是否区分大小写
}

def enhance_image_for_ocr(image_path):
    """图像增强处理"""
    # 使用OpenCV读取和处理
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"无法读取图像: {image_path}")
    
    results = []
    
    # 1. 原始图像
    with open(image_path, 'rb') as f:
        original_bytes = f.read()
    results.append(("original", original_bytes))
    
    # 2. 转换为灰度并放大
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 放大2倍
    height, width = gray.shape
    enlarged = cv2.resize(gray, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    
    # 保存临时文件并读取字节
    temp_path = "temp_enlarged.png"
    cv2.imwrite(temp_path, enlarged)
    with open(temp_path, 'rb') as f:
        enlarged_bytes = f.read()
    results.append(("enlarged_2x", enlarged_bytes))
    os.remove(temp_path)
    
    # 3. 自适应阈值处理
    adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
    
    # 放大并保存
    adaptive_enlarged = cv2.resize(adaptive, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    temp_path = "temp_adaptive.png"
    cv2.imwrite(temp_path, adaptive_enlarged)
    with open(temp_path, 'rb') as f:
        adaptive_bytes = f.read()
    results.append(("adaptive_threshold", adaptive_bytes))
    os.remove(temp_path)
    
    # 4. 对比度增强
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    enhanced_enlarged = cv2.resize(enhanced, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    
    temp_path = "temp_enhanced.png"
    cv2.imwrite(temp_path, enhanced_enlarged)
    with open(temp_path, 'rb') as f:
        enhanced_bytes = f.read()
    results.append(("enhanced_contrast", enhanced_bytes))
    os.remove(temp_path)
    
    # 5. 锐化处理
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(gray, -1, kernel)
    sharpened_enlarged = cv2.resize(sharpened, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
    
    temp_path = "temp_sharpened.png"
    cv2.imwrite(temp_path, sharpened_enlarged)
    with open(temp_path, 'rb') as f:
        sharpened_bytes = f.read()
    results.append(("sharpened", sharpened_bytes))
    os.remove(temp_path)
    
    return results

def validate_and_filter_result(text, config=CAPTCHA_CONFIG):
    """
    验证和过滤识别结果

    Args:
        text: 识别的原始文本
        config: 验证码配置

    Returns:
        过滤后的文本，如果不符合要求返回None
    """
    if not text:
        return None

    # 移除空格和特殊字符
    cleaned = re.sub(r'[^a-zA-Z0-9]', '', text)

    # 大小写处理
    if not config['case_sensitive']:
        cleaned = cleaned.lower()

    # 只保留允许的字符
    allowed_chars = config['chars'].lower() if not config['case_sensitive'] else config['chars']
    filtered = ''.join(c for c in cleaned if c in allowed_chars)

    # 长度检查
    if len(filtered) == config['length']:
        return filtered
    elif len(filtered) > config['length']:
        # 如果长度超出，取前N位
        return filtered[:config['length']]
    else:
        # 长度不足，返回原结果但标记为低置信度
        return filtered if filtered else None

def calculate_confidence_score(results, config=CAPTCHA_CONFIG):
    """
    计算识别结果的置信度分数

    Args:
        results: [(method, text), ...] 识别结果列表
        config: 验证码配置

    Returns:
        排序后的结果列表，包含置信度分数
    """
    # 过滤和标准化结果
    filtered_results = []
    for method, text in results:
        filtered_text = validate_and_filter_result(text, config)
        if filtered_text:
            filtered_results.append((method, filtered_text))

    if not filtered_results:
        return []

    # 统计每个结果的出现次数
    result_counts = {}
    for method, text in filtered_results:
        if text in result_counts:
            result_counts[text]['count'] += 1
            result_counts[text]['methods'].append(method)
        else:
            result_counts[text] = {'count': 1, 'methods': [method]}

    # 计算置信度分数
    scored_results = []
    total_attempts = len(filtered_results)

    for text, data in result_counts.items():
        # 基础分数：出现频率
        frequency_score = data['count'] / total_attempts

        # 长度匹配奖励
        length_score = 1.0 if len(text) == config['length'] else 0.5

        # 字符有效性奖励
        allowed_chars = config['chars'].lower() if not config['case_sensitive'] else config['chars']
        valid_chars = sum(1 for c in text if c in allowed_chars)
        char_score = valid_chars / len(text) if text else 0

        # 综合分数
        final_score = frequency_score * 0.6 + length_score * 0.3 + char_score * 0.1

        scored_results.append({
            'text': text,
            'count': data['count'],
            'methods': data['methods'],
            'frequency_score': frequency_score,
            'length_score': length_score,
            'char_score': char_score,
            'final_score': final_score
        })

    # 按最终分数排序
    scored_results.sort(key=lambda x: x['final_score'], reverse=True)
    return scored_results

def recognize_captcha(image_path, config=CAPTCHA_CONFIG):
    """
    识别验证码（增强版）

    Args:
        image_path: 图像文件路径
        config: 验证码配置

    Returns:
        最佳识别结果
    """
    print(f"开始识别图像: {image_path}")
    print(f"验证码配置: 长度={config['length']}, 字符类型=数字+字母")

    if not os.path.exists(image_path):
        print(f"文件不存在: {image_path}")
        return None

    try:
        # 初始化OCR
        ocr = ddddocr.DdddOcr()
        print("ddddocr 初始化成功")

        # 获取不同处理方式的图像
        processed_images = enhance_image_for_ocr(image_path)

        raw_results = []

        print("\n原始识别结果:")
        print("-" * 30)
        for method_name, img_bytes in processed_images:
            try:
                result = ocr.classification(img_bytes)
                if result and result.strip():
                    raw_results.append((method_name, result.strip()))
                    print(f"{method_name}: '{result.strip()}'")
                else:
                    print(f"{method_name}: 无结果")
            except Exception as e:
                print(f"{method_name} 识别失败: {e}")

        # 智能分析结果
        if raw_results:
            print("\n" + "="*60)
            print("智能分析结果:")
            print("="*60)

            # 计算置信度分数
            scored_results = calculate_confidence_score(raw_results, config)

            if scored_results:
                print(f"{'排名':<4} {'结果':<8} {'出现次数':<8} {'频率':<8} {'长度匹配':<10} {'最终分数':<10}")
                print("-" * 60)

                for i, result in enumerate(scored_results, 1):
                    print(f"{i:<4} {result['text']:<8} {result['count']:<8} "
                          f"{result['frequency_score']:.2f}{'':4} {result['length_score']:.2f}{'':6} "
                          f"{result['final_score']:.3f}")

                best_result = scored_results[0]
                print(f"\n🎯 最佳识别结果: '{best_result['text']}'")
                print(f"   置信度分数: {best_result['final_score']:.3f}")
                print(f"   识别方法: {', '.join(best_result['methods'])}")

                # 验证结果质量
                if best_result['final_score'] >= 0.8:
                    print("   ✅ 高置信度结果")
                elif best_result['final_score'] >= 0.6:
                    print("   ⚠️  中等置信度结果")
                else:
                    print("   ❌ 低置信度结果，建议重新截图")

                return best_result['text']
            else:
                print("❌ 所有识别结果都不符合验证码格式要求")
                print(f"   要求: {config['length']}位数字+字母组合")
                return None
        else:
            print("❌ 所有方法都未能识别出结果")
            return None

    except Exception as e:
        print(f"识别过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数 - 支持自定义配置"""
    # 修复PIL兼容性
    if not hasattr(Image, 'ANTIALIAS'):
        Image.ANTIALIAS = Image.LANCZOS

    # 可以根据实际验证码调整配置
    custom_config = {
        'length': 4,  # 当前验证码是4位，可根据需要调整
        'chars': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
        'case_sensitive': False,  # 不区分大小写，提高匹配率
    }

    image_path = "c.png"

    print("🔍 验证码智能识别系统")
    print("=" * 60)
    print(f"目标图像: {image_path}")
    print(f"验证码规则: {custom_config['length']}位数字+字母组合")
    print("=" * 60)

    result = recognize_captcha(image_path, custom_config)

    print("\n" + "=" * 60)
    if result:
        print(f"🎉 最终识别结果: {result}")
        print("💡 提示: 如果识别错误，可以尝试重新截图或调整验证码配置")
    else:
        print("❌ 识别失败")
        print("💡 建议:")
        print("   1. 检查图像是否清晰")
        print("   2. 确认验证码长度配置是否正确")
        print("   3. 尝试重新截图")
    print("=" * 60)

if __name__ == "__main__":
    main()