from PIL import ImageGrab
import time

# 匹配位置信息 (替换成你的实际坐标)
match_info = {
    "confidence": 0.9980,
    "top_left": (1354, 701),  # 左上角坐标
    "bottom_right": (1534, 776),  # 右下角坐标
    "size": (180, 75)  # 区域尺寸 (宽度, 高度)
}


def capture_area(top_left, bottom_right):
    """
    截取屏幕上指定矩形区域的图像

    参数:
    top_left - 区域左上角坐标 (x, y)
    bottom_right - 区域右下角坐标 (x, y)

    返回:
    PIL图像对象
    """
    # 验证坐标有效性
    if top_left[0] >= bottom_right[0] or top_left[1] >= bottom_right[1]:
        raise ValueError("无效坐标: 左上角必须小于右下角坐标")

    # 计算区域框 (left, top, right, bottom)
    capture_box = (
        top_left[0],
        top_left[1],
        bottom_right[0],
        bottom_right[1]
    )

    # 直接截取指定区域，不需要前置激活窗口
    screenshot = ImageGrab.grab(bbox=capture_box, all_screens=True)
    return screenshot


# 主程序
if __name__ == "__main__":
    time.sleep(1)
    try:
        # 获取坐标信息
        tl = match_info["top_left"]
        br = match_info["bottom_right"]

        # 执行截图
        start_time = time.time()
        img = capture_area(tl, br)

        # 生成包含坐标的文件名
        filename = f"c.png"
        img.save(filename)

        print(f"截图成功! 保存为: {filename}")
        print(f"耗时: {time.time() - start_time:.2f}秒")
        print(f"截图尺寸: {img.size[0]}x{img.size[1]}像素")

    except Exception as e:
        print(f"截图失败: {str(e)}")