"""
专门针对验证码的OCR识别脚本
结合tu2.py的截图功能和多种识别方法
"""
import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageGrab
import ddddocr
import time
import os
from typing import List, Tuple, Optional

# 从tu2.py导入的配置
match_info = {
    "confidence": 0.9980,
    "top_left": (1354, 701),
    "bottom_right": (1534, 776),
    "size": (180, 75)
}

class CaptchaOCR:
    """验证码OCR识别类"""
    
    def __init__(self):
        try:
            self.dddd_ocr = ddddocr.DdddOcr(show_ad=False)
        except TypeError:
            # 如果show_ad参数不支持，使用默认初始化
            self.dddd_ocr = ddddocr.DdddOcr()
        
    def capture_area(self, top_left, bottom_right):
        """截取屏幕指定区域 - 来自tu2.py"""
        if top_left[0] >= bottom_right[0] or top_left[1] >= bottom_right[1]:
            raise ValueError("无效坐标: 左上角必须小于右下角坐标")
        
        capture_box = (top_left[0], top_left[1], bottom_right[0], bottom_right[1])
        screenshot = ImageGrab.grab(bbox=capture_box, all_screens=True)
        return screenshot
    
    def preprocess_for_captcha(self, image_path: str) -> List[np.ndarray]:
        """
        专门针对验证码的预处理方法
        返回多个处理后的图像供识别
        """
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        processed_images = []
        
        # 1. 基础灰度处理
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        processed_images.append(("gray", gray))
        
        # 2. 高斯模糊去噪 + 锐化
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(blurred, -1, kernel)
        processed_images.append(("sharpened", sharpened))
        
        # 3. 自适应阈值 - 对验证码很有效
        adaptive_thresh = cv2.adaptiveThreshold(
            gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        processed_images.append(("adaptive_thresh", adaptive_thresh))
        
        # 4. OTSU阈值
        _, otsu_thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        processed_images.append(("otsu_thresh", otsu_thresh))
        
        # 5. 形态学操作去除噪点
        kernel = np.ones((2,2), np.uint8)
        morphed = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
        morphed = cv2.morphologyEx(morphed, cv2.MORPH_OPEN, kernel)
        processed_images.append(("morphed", morphed))
        
        # 6. 对比度增强
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        processed_images.append(("enhanced", enhanced))
        
        return processed_images
    
    def resize_for_ocr(self, img: np.ndarray, scale_factors: List[float] = [2.0, 3.0]) -> List[np.ndarray]:
        """放大图像提高OCR精度"""
        resized_images = []
        for scale in scale_factors:
            height, width = img.shape[:2]
            new_width = int(width * scale)
            new_height = int(height * scale)
            resized = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
            resized_images.append((f"scale_{scale}", resized))
        return resized_images
    
    def recognize_captcha(self, image_path: str) -> dict:
        """识别验证码的主方法"""
        results = {
            'attempts': [],
            'best_result': '',
            'confidence_score': {}
        }
        
        print(f"开始识别验证码: {image_path}")
        
        # 预处理图像
        processed_images = self.preprocess_for_captcha(image_path)
        
        for process_name, processed_img in processed_images:
            # 对每个预处理结果尝试不同的放大倍数
            resized_images = self.resize_for_ocr(processed_img)
            
            for resize_name, resized_img in resized_images:
                try:
                    # 转换为字节进行识别
                    _, img_bytes = cv2.imencode('.png', resized_img)
                    result = self.dddd_ocr.classification(img_bytes.tobytes())
                    
                    if result and result.strip():
                        method_name = f"{process_name}_{resize_name}"
                        results['attempts'].append({
                            'method': method_name,
                            'result': result.strip(),
                            'length': len(result.strip())
                        })
                        print(f"  {method_name}: {result.strip()}")
                        
                except Exception as e:
                    print(f"  {process_name}_{resize_name} 识别失败: {e}")
        
        # 分析结果
        if results['attempts']:
            # 统计每个结果出现的次数
            result_counts = {}
            for attempt in results['attempts']:
                text = attempt['result']
                if text in result_counts:
                    result_counts[text] += 1
                else:
                    result_counts[text] = 1
            
            # 找出最可能的结果
            best_result = max(result_counts.items(), key=lambda x: x[1])
            results['best_result'] = best_result[0]
            results['confidence_score'] = result_counts
            
            print(f"\n最可能的结果: '{best_result[0]}' (出现 {best_result[1]} 次)")
        
        return results
    
    def capture_and_recognize(self) -> dict:
        """截图并识别验证码"""
        try:
            # 截图
            tl = match_info["top_left"]
            br = match_info["bottom_right"]
            
            print("正在截图...")
            img = self.capture_area(tl, br)
            
            # 保存截图
            filename = f"captcha_{int(time.time())}.png"
            img.save(filename)
            print(f"截图保存为: {filename}")
            
            # 识别
            results = self.recognize_captcha(filename)
            results['screenshot_file'] = filename
            
            return results
            
        except Exception as e:
            print(f"截图或识别失败: {e}")
            return {'error': str(e)}

def main():
    """主函数"""
    ocr = CaptchaOCR()
    
    # 检查是否有现有的c.png文件
    if os.path.exists("c.png"):
        print("发现现有的 c.png 文件，开始识别...")
        results = ocr.recognize_captcha("c.png")
        
        print("\n" + "="*50)
        print("识别结果:")
        print("="*50)
        
        if results['best_result']:
            print(f"最佳识别结果: {results['best_result']}")
            print(f"置信度统计: {results['confidence_score']}")
        else:
            print("未能识别出有效结果")
            
        print(f"总尝试次数: {len(results['attempts'])}")
    
    # 询问是否进行实时截图识别
    choice = input("\n是否进行实时截图识别? (y/n): ").lower()
    if choice == 'y':
        print("3秒后开始截图...")
        time.sleep(3)
        
        results = ocr.capture_and_recognize()
        
        if 'error' not in results:
            print("\n" + "="*50)
            print("实时识别结果:")
            print("="*50)
            
            if results['best_result']:
                print(f"最佳识别结果: {results['best_result']}")
                print(f"置信度统计: {results['confidence_score']}")
            else:
                print("未能识别出有效结果")

if __name__ == "__main__":
    main()
