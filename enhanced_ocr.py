import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import ddddocr
import easyocr
import pytesseract
import os
from typing import List, Tuple, Optional

class EnhancedOCR:
    """增强的OCR识别类，结合多种预处理和识别方法"""
    
    def __init__(self):
        # 初始化各种OCR引擎
        try:
            self.dddd_ocr = ddddocr.DdddOcr(show_ad=False)
        except TypeError:
            # 如果show_ad参数不支持，使用默认初始化
            self.dddd_ocr = ddddocr.DdddOcr()

        try:
            self.easy_reader = easyocr.Reader(['ch_sim', 'en'])  # 中英文
        except:
            self.easy_reader = None
            print("EasyOCR 初始化失败，将跳过EasyOCR识别")
        
    def preprocess_image(self, image_path: str, method: str = "default") -> np.ndarray:
        """
        图像预处理方法
        
        Args:
            image_path: 图像路径
            method: 预处理方法 ('default', 'denoise', 'enhance', 'threshold', 'morphology')
        
        Returns:
            处理后的图像数组
        """
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        if method == "denoise":
            # 降噪处理
            gray = cv2.fastNlMeansDenoising(gray)
            
        elif method == "enhance":
            # 对比度增强
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            gray = clahe.apply(gray)
            
        elif method == "threshold":
            # 自适应阈值
            gray = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY, 11, 2)
            
        elif method == "morphology":
            # 形态学操作
            kernel = np.ones((2,2), np.uint8)
            gray = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
            gray = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel)
            
        return gray
    
    def resize_image(self, img: np.ndarray, scale_factor: float = 2.0) -> np.ndarray:
        """放大图像以提高识别精度"""
        height, width = img.shape[:2]
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        return cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
    
    def pil_enhance(self, image_path: str) -> Image.Image:
        """使用PIL进行图像增强"""
        img = Image.open(image_path)
        
        # 锐化
        img = img.filter(ImageFilter.SHARPEN)
        
        # 对比度增强
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(1.5)
        
        # 亮度调整
        enhancer = ImageEnhance.Brightness(img)
        img = enhancer.enhance(1.2)
        
        return img
    
    def recognize_with_ddddocr(self, image_path: str, preprocess_methods: List[str] = None) -> List[str]:
        """使用ddddocr识别，尝试多种预处理方法"""
        results = []
        
        if preprocess_methods is None:
            preprocess_methods = ["default", "denoise", "enhance", "threshold"]
        
        for method in preprocess_methods:
            try:
                # 预处理
                processed_img = self.preprocess_image(image_path, method)
                
                # 放大图像
                resized_img = self.resize_image(processed_img, 2.0)
                
                # 转换为字节
                _, img_bytes = cv2.imencode('.png', resized_img)
                
                # 识别
                result = self.dddd_ocr.classification(img_bytes.tobytes())
                if result and result.strip():
                    results.append(f"ddddocr_{method}: {result}")
                    
            except Exception as e:
                print(f"ddddocr {method} 方法失败: {e}")
        
        return results
    
    def recognize_with_easyocr(self, image_path: str) -> List[str]:
        """使用EasyOCR识别"""
        results = []
        
        try:
            # 原图识别
            result = self.easy_reader.readtext(image_path, detail=0)
            if result:
                results.append(f"easyocr_original: {''.join(result)}")
            
            # 预处理后识别
            for method in ["enhance", "threshold"]:
                processed_img = self.preprocess_image(image_path, method)
                resized_img = self.resize_image(processed_img, 2.0)
                
                # 保存临时文件
                temp_path = f"temp_{method}.png"
                cv2.imwrite(temp_path, resized_img)
                
                result = self.easy_reader.readtext(temp_path, detail=0)
                if result:
                    results.append(f"easyocr_{method}: {''.join(result)}")
                
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    
        except Exception as e:
            print(f"EasyOCR 识别失败: {e}")
        
        return results
    
    def recognize_with_tesseract(self, image_path: str) -> List[str]:
        """使用Tesseract识别"""
        results = []
        
        try:
            # PIL增强后识别
            enhanced_img = self.pil_enhance(image_path)
            
            # 尝试不同的配置
            configs = [
                '--psm 8 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
                '--psm 7',
                '--psm 6'
            ]
            
            for i, config in enumerate(configs):
                try:
                    result = pytesseract.image_to_string(enhanced_img, config=config).strip()
                    if result:
                        results.append(f"tesseract_config{i}: {result}")
                except:
                    continue
                    
        except Exception as e:
            print(f"Tesseract 识别失败: {e}")
        
        return results
    
    def comprehensive_recognize(self, image_path: str) -> dict:
        """综合识别方法，使用多种OCR引擎和预处理方法"""
        all_results = {
            'ddddocr': [],
            'easyocr': [],
            'tesseract': [],
            'summary': {}
        }
        
        print(f"开始识别图像: {image_path}")
        
        # ddddocr识别
        print("正在使用 ddddocr 识别...")
        all_results['ddddocr'] = self.recognize_with_ddddocr(image_path)
        
        # EasyOCR识别
        print("正在使用 EasyOCR 识别...")
        all_results['easyocr'] = self.recognize_with_easyocr(image_path)
        
        # Tesseract识别
        print("正在使用 Tesseract 识别...")
        all_results['tesseract'] = self.recognize_with_tesseract(image_path)
        
        # 统计结果
        all_texts = []
        for engine_results in [all_results['ddddocr'], all_results['easyocr'], all_results['tesseract']]:
            for result in engine_results:
                text = result.split(': ', 1)[1] if ': ' in result else result
                all_texts.append(text)
        
        # 找出最常见的结果
        from collections import Counter
        if all_texts:
            text_counts = Counter(all_texts)
            most_common = text_counts.most_common(3)
            all_results['summary'] = {
                'most_likely': most_common[0][0] if most_common else '',
                'confidence_ranking': most_common,
                'total_attempts': len(all_texts)
            }
        
        return all_results

def main():
    """主函数"""
    image_path = "c.png"
    
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        return
    
    # 创建OCR实例
    ocr = EnhancedOCR()
    
    # 执行综合识别
    results = ocr.comprehensive_recognize(image_path)
    
    # 打印结果
    print("\n" + "="*50)
    print("识别结果汇总:")
    print("="*50)
    
    for engine, engine_results in results.items():
        if engine != 'summary' and engine_results:
            print(f"\n{engine.upper()} 结果:")
            for result in engine_results:
                print(f"  - {result}")
    
    if results['summary']:
        print(f"\n最可能的结果: {results['summary']['most_likely']}")
        print(f"总尝试次数: {results['summary']['total_attempts']}")
        print("\n置信度排名:")
        for text, count in results['summary']['confidence_ranking']:
            print(f"  '{text}': 出现 {count} 次")

if __name__ == "__main__":
    main()
