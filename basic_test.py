"""
基础测试脚本
"""
import ddddocr
import cv2
import os

def test_basic_ocr():
    image_path = "c.png"
    
    print(f"测试文件: {image_path}")
    print(f"文件存在: {os.path.exists(image_path)}")
    
    if not os.path.exists(image_path):
        print("文件不存在，退出")
        return
    
    try:
        # 初始化OCR
        print("初始化ddddocr...")
        ocr = ddddocr.DdddOcr()
        print("ddddocr初始化成功")
        
        # 读取图像
        print("读取图像...")
        with open(image_path, 'rb') as f:
            img_bytes = f.read()
        print(f"图像大小: {len(img_bytes)} 字节")
        
        # 识别
        print("开始识别...")
        result = ocr.classification(img_bytes)
        print(f"识别结果: '{result}'")
        
        # 使用OpenCV预处理
        print("\n使用OpenCV预处理...")
        img = cv2.imread(image_path)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 放大2倍
        height, width = gray.shape
        resized = cv2.resize(gray, (width*2, height*2), interpolation=cv2.INTER_CUBIC)
        
        # 保存临时文件
        temp_path = "temp_processed.png"
        cv2.imwrite(temp_path, resized)
        
        # 识别处理后的图像
        with open(temp_path, 'rb') as f:
            processed_bytes = f.read()
        
        result2 = ocr.classification(processed_bytes)
        print(f"预处理后识别结果: '{result2}'")
        
        # 清理临时文件
        if os.path.exists(temp_path):
            os.remove(temp_path)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_ocr()
