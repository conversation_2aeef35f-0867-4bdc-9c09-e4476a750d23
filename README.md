# 🎯 验证码智能识别系统

## 📋 功能特点

### ✨ 核心优化
1. **针对数字+字母验证码优化** - 专门处理混合字符类型
2. **可配置验证码长度** - 支持4位、6位等不同长度（当前默认4位）
3. **智能结果过滤** - 自动过滤无效字符和错误长度
4. **多重置信度评估** - 综合频率、长度、字符有效性评分
5. **多种图像预处理** - 5种不同的图像增强方法

### 🔧 技术改进
- **PIL兼容性修复** - 解决新版本PIL的ANTIALIAS问题
- **结果验证机制** - 确保识别结果符合验证码规则
- **置信度评分系统** - 智能选择最可靠的识别结果

## 📁 文件说明

| 文件名 | 功能描述 |
|--------|----------|
| `fixed_ocr.py` | **主要脚本** - 增强版验证码识别 |
| `captcha_config.py` | 配置文件 - 不同验证码类型的参数设置 |
| `tu2.py` | 截图脚本 - 从屏幕指定区域截取验证码 |
| `test_environment.py` | 环境测试 - 检查依赖是否正确安装 |
| `simple_ocr.py` | 简化版本 - 基础识别功能 |

## 🚀 使用方法

### 1. 环境检查
```bash
python test_environment.py
```

### 2. 安装依赖（如果需要）
```bash
pip install ddddocr opencv-python
```

### 3. 识别验证码
```bash
python fixed_ocr.py
```

## ⚙️ 配置说明

### 默认配置（适用于当前验证码）
```python
CAPTCHA_CONFIG = {
    'length': 4,  # 4位验证码
    'chars': 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',  # 数字+字母
    'case_sensitive': False,  # 不区分大小写
}
```

### 自定义配置示例
```python
# 6位纯数字验证码
config = {
    'length': 6,
    'chars': '0123456789',
    'case_sensitive': False,
}

# 4位纯字母验证码
config = {
    'length': 4,
    'chars': 'abcdefghijklmnopqrstuvwxyz',
    'case_sensitive': False,
}
```

## 📊 识别效果

### 输出示例
```
🔍 验证码智能识别系统
============================================================
目标图像: c.png
验证码规则: 4位数字+字母组合
============================================================

原始识别结果:
------------------------------
original: 'bgvb'
enlarged_2x: 'bgvb'
adaptive_threshold: 'bgv8'
enhanced_contrast: 'bgvb'
sharpened: 'bgvb'

============================================================
智能分析结果:
============================================================
排名   结果     出现次数   频率     长度匹配     最终分数
------------------------------------------------------------
1    <USER>     <GROUP>        0.80    1.00      0.890
2    bgv8     1        0.20    1.00      0.320

🎯 最佳识别结果: 'bgvb'
   置信度分数: 0.890
   识别方法: original, enlarged_2x, enhanced_contrast, sharpened
   ✅ 高置信度结果
```

## 🎯 提升识别精准度的关键点

### 1. **验证码特征约束**
- ✅ **明确字符类型**: 数字+字母混合
- ✅ **固定长度**: 4位（可配置）
- ✅ **大小写处理**: 统一转换为小写提高匹配率

### 2. **智能结果过滤**
- 自动移除特殊字符和空格
- 长度验证和截取
- 字符有效性检查

### 3. **多维度置信度评估**
- **频率权重 (60%)**: 相同结果出现的次数
- **长度权重 (30%)**: 是否符合预期长度
- **字符权重 (10%)**: 字符是否在允许范围内

### 4. **图像预处理优化**
- 原始图像识别
- 2倍放大处理
- 自适应阈值二值化
- 对比度增强 (CLAHE)
- 锐化滤波

## 💡 使用建议

### 高精度识别技巧
1. **确保截图清晰** - 避免模糊和噪点
2. **调整配置参数** - 根据实际验证码类型修改配置
3. **多次尝试** - 如果置信度低，重新截图识别
4. **检查结果长度** - 确认识别结果符合预期位数

### 常见问题解决
- **识别结果为空**: 检查图像是否清晰，尝试重新截图
- **长度不匹配**: 调整配置中的 `length` 参数
- **字符类型错误**: 修改配置中的 `chars` 参数
- **置信度低**: 多次截图取最佳结果

## 🔄 工作流程

1. **截图** → `tu2.py` 获取验证码图像
2. **预处理** → 5种不同的图像增强方法
3. **识别** → ddddocr 对每种预处理结果进行识别
4. **过滤** → 根据验证码规则过滤无效结果
5. **评分** → 多维度置信度评估
6. **输出** → 返回最高置信度的识别结果

## 📈 性能表现

- **识别准确率**: 显著提升（通过多方法投票）
- **处理速度**: 快速（5种方法并行处理）
- **稳定性**: 高（智能过滤和置信度评估）
- **适应性**: 强（可配置不同验证码类型）

---

**💡 提示**: 这个系统特别针对4位数字+字母验证码进行了优化，通过智能分析和多重验证大大提高了识别的准确性和可靠性。
