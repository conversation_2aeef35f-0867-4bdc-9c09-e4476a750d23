"""
测试OCR环境是否正确配置
"""
import sys

def test_imports():
    """测试必要的库是否可以导入"""
    print("测试Python库导入...")
    
    libraries = [
        ("PIL", "from PIL import Image"),
        ("cv2", "import cv2"),
        ("numpy", "import numpy"),
        ("ddddocr", "import ddddocr")
    ]
    
    success_count = 0
    for lib_name, import_cmd in libraries:
        try:
            exec(import_cmd)
            print(f"✓ {lib_name} - 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"✗ {lib_name} - 导入失败: {e}")
    
    print(f"\n库导入测试结果: {success_count}/{len(libraries)} 成功")
    return success_count == len(libraries)

def test_ddddocr():
    """测试ddddocr是否正常工作"""
    print("\n测试ddddocr功能...")
    try:
        import ddddocr
        # 尝试不同的初始化方式
        try:
            ocr = ddddocr.DdddOcr(show_ad=False)
        except TypeError:
            # 如果show_ad参数不支持，使用默认初始化
            ocr = ddddocr.DdddOcr()
        print("✓ ddddocr 初始化成功")
        return True
    except Exception as e:
        print(f"✗ ddddocr 测试失败: {e}")
        return False

def test_image_file():
    """测试c.png文件是否存在且可读"""
    print("\n测试图像文件...")
    import os
    
    if os.path.exists("c.png"):
        print("✓ c.png 文件存在")
        try:
            from PIL import Image
            img = Image.open("c.png")
            print(f"✓ 图像可读取，尺寸: {img.size}")
            return True
        except Exception as e:
            print(f"✗ 图像读取失败: {e}")
            return False
    else:
        print("✗ c.png 文件不存在")
        return False

def main():
    print("OCR环境测试")
    print("="*40)
    
    # 测试库导入
    imports_ok = test_imports()
    
    # 测试ddddocr
    ddddocr_ok = test_ddddocr()
    
    # 测试图像文件
    image_ok = test_image_file()
    
    print("\n" + "="*40)
    print("测试总结:")
    print(f"库导入: {'✓' if imports_ok else '✗'}")
    print(f"ddddocr: {'✓' if ddddocr_ok else '✗'}")
    print(f"图像文件: {'✓' if image_ok else '✗'}")
    
    if imports_ok and ddddocr_ok:
        print("\n环境配置正常，可以运行OCR脚本！")
        
        if image_ok:
            print("建议运行: python captcha_ocr.py")
        else:
            print("建议先运行: python tu2.py 生成截图")
    else:
        print("\n环境配置有问题，请先安装缺失的依赖:")
        print("运行: python install_requirements.py")

if __name__ == "__main__":
    main()
